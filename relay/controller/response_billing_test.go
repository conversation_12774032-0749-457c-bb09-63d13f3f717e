package controller

import (
	"testing"
)

// TestResponseAPIBillingIntegration tests that Response API billing follows DRY principle
// and produces the same billing results as ChatCompletion API
func TestResponseAPIBillingIntegration(t *testing.T) {
	// Test data
	promptTokens := 18
	completionTokens := 35
	modelRatio := 1.0
	groupRatio := 1.0
	completionRatio := 1.0
	toolsCost := int64(0)

	// Test parameters
	ratio := modelRatio * groupRatio
	preConsumedQuota := int64(20) // Some pre-consumed amount

	// Calculate expected quota using the same formula as the actual implementation
	expectedQuota := int64((float64(promptTokens)+float64(completionTokens)*completionRatio)*ratio) + toolsCost
	if ratio != 0 && expectedQuota <= 0 {
		expectedQuota = 1
	}

	// Test the Response API billing calculation logic (without database operations)
	t.Run("Response API Billing Calculation", func(t *testing.T) {
		// Test the quota calculation formula directly
		// This is the same formula used in postConsumeResponseAPIQuota
		calculatedQuota := int64((float64(promptTokens)+float64(completionTokens)*completionRatio)*ratio) + toolsCost
		if ratio != 0 && calculatedQuota <= 0 {
			calculatedQuota = 1
		}

		// Verify the quota calculation
		if calculatedQuota != expectedQuota {
			t.Errorf("Expected quota %d, got %d", expectedQuota, calculatedQuota)
		}

		t.Logf("Response API billing calculation test passed: quota=%d, promptTokens=%d, completionTokens=%d",
			calculatedQuota, promptTokens, completionTokens)
	})

	// Test that the billing calculation is consistent
	t.Run("Billing Consistency Check", func(t *testing.T) {
		quotaDelta := expectedQuota - preConsumedQuota

		// Verify that quota delta calculation is correct
		if quotaDelta != 33 { // 53 - 20 = 33 for our test data
			t.Errorf("Expected quotaDelta to be 33, got %d", quotaDelta)
		}

		t.Logf("Billing consistency test passed: quotaDelta=%d, totalQuota=%d", quotaDelta, expectedQuota)
	})
}

// TestLegacyChatCompletionBilling tests that legacy ChatCompletion API billing still works
func TestLegacyChatCompletionBilling(t *testing.T) {
	// Test data matching ChatCompletion API
	promptTokens := 25
	completionTokens := 50
	modelRatio := 1.5
	groupRatio := 0.8
	completionRatio := 1.2
	toolsCost := int64(5)

	// Test parameters
	ratio := modelRatio * groupRatio

	// Calculate expected quota using ChatCompletion formula
	expectedQuota := int64((float64(promptTokens)+float64(completionTokens)*completionRatio)*ratio) + toolsCost
	if ratio != 0 && expectedQuota <= 0 {
		expectedQuota = 1
	}

	t.Run("ChatCompletion Billing Calculation", func(t *testing.T) {
		// Test the quota calculation formula used in ChatCompletion
		calculatedQuota := int64((float64(promptTokens)+float64(completionTokens)*completionRatio)*ratio) + toolsCost
		if ratio != 0 && calculatedQuota <= 0 {
			calculatedQuota = 1
		}

		// Verify the quota calculation
		if calculatedQuota != expectedQuota {
			t.Errorf("Expected quota %d, got %d", expectedQuota, calculatedQuota)
		}

		// Expected: (25 + 50*1.2) * 1.5 * 0.8 + 5 = (25 + 60) * 1.2 + 5 = 85 * 1.2 + 5 = 102 + 5 = 107
		if calculatedQuota != 107 {
			t.Errorf("Expected quota to be 107, got %d", calculatedQuota)
		}

		t.Logf("ChatCompletion billing test passed: quota=%d, promptTokens=%d, completionTokens=%d",
			calculatedQuota, promptTokens, completionTokens)
	})
}

// TestBillingConsistency tests that Response API and ChatCompletion API produce consistent billing
func TestBillingConsistency(t *testing.T) {
	testCases := []struct {
		name             string
		promptTokens     int
		completionTokens int
		modelRatio       float64
		groupRatio       float64
		completionRatio  float64
		toolsCost        int64
	}{
		{
			name:             "Simple request",
			promptTokens:     18,
			completionTokens: 35,
			modelRatio:       1.0,
			groupRatio:       1.0,
			completionRatio:  1.0,
			toolsCost:        0,
		},
		{
			name:             "Request with tools cost",
			promptTokens:     50,
			completionTokens: 100,
			modelRatio:       1.5,
			groupRatio:       0.8,
			completionRatio:  1.2,
			toolsCost:        10,
		},
		{
			name:             "High completion ratio",
			promptTokens:     25,
			completionTokens: 75,
			modelRatio:       2.0,
			groupRatio:       1.0,
			completionRatio:  3.0,
			toolsCost:        0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Calculate quota using the billing formula
			ratio := tc.modelRatio * tc.groupRatio
			expectedQuota := int64((float64(tc.promptTokens)+float64(tc.completionTokens)*tc.completionRatio)*ratio) + tc.toolsCost
			if ratio != 0 && expectedQuota <= 0 {
				expectedQuota = 1
			}

			t.Logf("Test case: %s", tc.name)
			t.Logf("  Input: promptTokens=%d, completionTokens=%d", tc.promptTokens, tc.completionTokens)
			t.Logf("  Ratios: model=%.2f, group=%.2f, completion=%.2f", tc.modelRatio, tc.groupRatio, tc.completionRatio)
			t.Logf("  Tools cost: %d", tc.toolsCost)
			t.Logf("  Expected quota: %d", expectedQuota)

			// Verify the calculation is reasonable
			if expectedQuota <= 0 {
				t.Errorf("Expected quota should be positive, got %d", expectedQuota)
			}

			// Verify that tools cost is properly added
			baseQuota := int64((float64(tc.promptTokens) + float64(tc.completionTokens)*tc.completionRatio) * ratio)
			if expectedQuota != baseQuota+tc.toolsCost {
				t.Errorf("Tools cost not properly added: expected %d, got %d", baseQuota+tc.toolsCost, expectedQuota)
			}
		})
	}
}
