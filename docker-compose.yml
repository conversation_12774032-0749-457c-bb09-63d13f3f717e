version: '3.4'

services:
  one-api:
    image: "${REGISTRY:-docker.io}/justsong/one-api:latest"
    container_name: one-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./data/oneapi:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=oneapi:123456@tcp(db:3306)/one-api  # Modify this line or comment it out to use SQLite as database
      - REDIS_CONN_STRING=redis://redis
      - SESSION_SECRET=random_string  # Change to a random string
      - TZ=Asia/Shanghai
#      - NODE_TYPE=slave  # Uncomment this line for slave nodes in multi-machine deployment
#      - SYNC_FREQUENCY=60  # Uncomment this line when periodic data loading from database is needed
#      - FRONTEND_BASE_URL=https://openai.justsong.cn  # Uncomment this line for slave nodes in multi-machine deployment
    depends_on:
      - redis
      - db
    healthcheck:
      test: [ "CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $2}'" ]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: "${REGISTRY:-docker.io}/redis:latest"
    container_name: redis
    restart: always

  db:
    image: "${REGISTRY:-docker.io}/mysql:8.2.0"
    restart: always
    container_name: mysql
    volumes:
      - ./data/mysql:/var/lib/mysql  # Mount directory for persistent storage
    ports:
      - '3306:3306'
    environment:
      TZ: Asia/Shanghai   # Set timezone
      MYSQL_ROOT_PASSWORD: 'OneAPI@justsong' # Set root user password
      MYSQL_USER: oneapi   # Create dedicated user
      MYSQL_PASSWORD: '123456'    # Set dedicated user password
      MYSQL_DATABASE: one-api   # Automatically create database
