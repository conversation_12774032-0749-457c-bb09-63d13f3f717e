/* Enhanced Mobile-responsive table styles */
@media (max-width: 768px) {
  /* Hide table headers on mobile */
  .mobile-table thead,
  table thead {
    display: none;
  }

  /* Convert table rows to cards on mobile */
  .mobile-table tbody tr,
  .mobile-table-row,
  table tbody tr {
    display: block;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    background-color: hsl(var(--card));
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;
  }

  .mobile-table tbody tr:hover,
  .mobile-table-row:hover,
  table tbody tr:hover {
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  }

  /* Style table cells as label-value pairs */
  .mobile-table tbody td,
  .mobile-table-cell,
  table tbody td {
    display: block;
    text-align: left !important;
    padding: 0.5rem 0;
    border: none;
    position: relative;
    word-break: break-word;
  }

  /* Add labels before content using data-label attribute */
  .mobile-table tbody td:before,
  .mobile-table-cell:before,
  table tbody td:before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: hsl(var(--muted-foreground));
    display: inline-block;
    min-width: 80px;
    margin-right: 0.5rem;
    font-size: 0.875rem;
  }

  /* Special handling for action buttons */
  .mobile-table tbody td:last-child,
  .mobile-table-cell:last-child,
  table tbody td:last-child {
    padding-top: 1rem;
    margin-top: 0.5rem;
    border-top: 1px solid hsl(var(--border));
  }

  .mobile-table tbody td:last-child:before,
  .mobile-table-cell:last-child:before,
  table tbody td:last-child:before {
    content: '';
    display: none;
  }

  /* Enhanced action buttons layout */
  .mobile-table .actions-grid,
  .mobile-table-cell .space-x-2,
  .mobile-table-cell .flex.gap-1,
  .mobile-table-cell .flex.gap-2 {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    width: 100%;
  }

  .mobile-table .actions-grid .primary-action,
  .mobile-table-cell .primary-action {
    grid-column: span 2;
  }

  /* Touch-friendly button sizing */
  .mobile-table button,
  .mobile-table-cell button,
  table button {
    min-height: 44px;
    min-width: 44px;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
  }

  /* Compact buttons for mobile */
  .mobile-table button.btn-compact,
  .mobile-table-cell button.btn-compact {
    min-height: 36px;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  /* Responsive pagination */
  .pagination-mobile {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-mobile .pagination-info {
    text-align: center;
    order: 1;
    font-size: 0.75rem;
  }

  .pagination-mobile .pagination-controls {
    justify-content: center;
    order: 2;
  }

  .pagination-mobile .pagination-size {
    justify-content: center;
    order: 3;
  }

  /* Enhanced pagination button spacing */
  .pagination-mobile .pagination-controls button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Hide some pagination buttons on small screens */
  @media (max-width: 480px) {
    .pagination-mobile .pagination-controls button:first-child,
    .pagination-mobile .pagination-controls button:last-child {
      display: none;
    }
  }
}

/* Enhanced touch targets and interactions */
@media (hover: none) and (pointer: coarse) {
  button, [role="button"], input[type="button"], input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  .touch-friendly {
    padding: 0.75rem;
    touch-action: manipulation;
  }

  /* Larger tap targets for small interactive elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  /* Enhanced focus styles for touch devices */
  button:focus-visible,
  [role="button"]:focus-visible {
    outline: 3px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Remove hover effects on touch devices */
  button:hover,
  [role="button"]:hover {
    background-color: initial;
    color: initial;
  }

  /* Enhanced active states for better feedback */
  button:active,
  [role="button"]:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
  }
}

/* Search dropdown mobile styles */
@media (max-width: 768px) {
  .search-dropdown {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    max-height: none !important;
    border-radius: 0 !important;
    z-index: 50;
  }

  .search-dropdown .search-content {
    height: 100%;
    overflow-y: auto;
    padding: 1rem;
  }

  .search-dropdown .search-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 51;
  }
}

/* Form improvements for mobile */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr !important;
  }

  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-actions button {
    width: 100%;
  }

  /* JSON editor improvements */
  .json-editor {
    font-size: 0.75rem;
    line-height: 1.4;
  }

  /* Model selection improvements */
  .model-grid {
    max-height: 200px;
    overflow-y: auto;
  }

  .model-item {
    padding: 0.75rem;
    margin: 0.25rem 0;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid;
  }

  button {
    border: 2px solid;
  }

  .badge {
    border: 1px solid;
  }
}

/* Focus management */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .mobile-table tbody tr {
    background-color: #1f2937;
    border-color: #374151;
  }

  .mobile-table tbody td:before {
    color: #d1d5db;
  }

  .mobile-table tbody td:last-child {
    border-top-color: #374151;
  }
}

/* Additional responsive utilities */
@layer utilities {
  /* Responsive text sizing */
  .text-responsive-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  @media (min-width: 768px) {
    .text-responsive-xs {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
  }

  .text-responsive-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  @media (min-width: 768px) {
    .text-responsive-sm {
      font-size: 1rem;
      line-height: 1.5rem;
    }
  }

  .text-responsive-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  @media (min-width: 768px) {
    .text-responsive-base {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  .text-responsive-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  @media (min-width: 768px) {
    .text-responsive-lg {
      font-size: 1.25rem;
      line-height: 1.75rem;
    }
  }

  @media (min-width: 1024px) {
    .text-responsive-lg {
      font-size: 1.5rem;
      line-height: 2rem;
    }
  }

  /* Responsive spacing utilities */
  .space-responsive-y > * + * {
    margin-top: 1rem;
  }

  @media (min-width: 768px) {
    .space-responsive-y > * + * {
      margin-top: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .space-responsive-y > * + * {
      margin-top: 2rem;
    }
  }

  .space-responsive-x > * + * {
    margin-left: 0.5rem;
  }

  @media (min-width: 768px) {
    .space-responsive-x > * + * {
      margin-left: 1rem;
    }
  }

  /* Responsive padding utilities */
  .p-responsive {
    padding: 1rem;
  }

  @media (min-width: 768px) {
    .p-responsive {
      padding: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .p-responsive {
      padding: 2rem;
    }
  }

  .px-responsive {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 768px) {
    .px-responsive {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .px-responsive {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .py-responsive {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  @media (min-width: 768px) {
    .py-responsive {
      padding-top: 1.5rem;
      padding-bottom: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .py-responsive {
      padding-top: 2rem;
      padding-bottom: 2rem;
    }
  }

  /* Responsive margin utilities */
  .m-responsive {
    margin: 1rem;
  }

  @media (min-width: 768px) {
    .m-responsive {
      margin: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .m-responsive {
      margin: 2rem;
    }
  }

  /* Responsive gap utilities */
  .gap-responsive {
    gap: 0.5rem;
  }

  @media (min-width: 768px) {
    .gap-responsive {
      gap: 1rem;
    }
  }

  @media (min-width: 1024px) {
    .gap-responsive {
      gap: 1.5rem;
    }
  }

  /* Responsive border radius */
  .rounded-responsive {
    border-radius: 0.375rem;
  }

  @media (min-width: 768px) {
    .rounded-responsive {
      border-radius: 0.5rem;
    }
  }

  @media (min-width: 1024px) {
    .rounded-responsive {
      border-radius: 0.75rem;
    }
  }
}
