.dashboard-container {
    padding: 20px 24px 40px;
    background-color: var(--bg-primary);
    margin-top: -15px; /* 减小与导航栏的间距 */
    max-width: 1600px;        /* 设置最大宽度 */
    margin-left: auto;        /* 水平居中 */
    margin-right: auto;
}

/* Modern Summary Cards */
.summary-cards-grid {
    margin-bottom: 2rem !important;
}

.summary-cards-grid .column {
    padding: 0.5rem !important;
}

.summary-card {
    height: 100%;
    border: none !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    overflow: hidden;
    position: relative;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
}

.requests-card {
    --gradient-start: #4318FF;
    --gradient-end: #6C63FF;
}

.quota-card {
    --gradient-start: #00B5D8;
    --gradient-end: #05CD99;
}

.tokens-card {
    --gradient-start: #FF5E7D;
    --gradient-end: #FFB547;
}

.efficiency-card {
    --gradient-start: #8B5CF6;
    --gradient-end: #EC4899;
}

.summary-card-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

.summary-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    color: white;
    flex-shrink: 0;
}

.summary-stats {
    flex: 1;
}

.summary-card .ui.statistic {
    margin: 0 !important;
}

.summary-card .ui.statistic > .value {
    color: var(--text-primary) !important;
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
}

.summary-card .ui.statistic > .label {
    color: var(--text-secondary) !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    margin-top: 0.25rem !important;
    text-transform: none !important;
}

/* Trend indicators */
.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.trend-value.positive {
    color: var(--success-color);
}

.trend-value.negative {
    color: var(--error-color);
}

.trend-value.neutral {
    color: var(--warning-color);
}

/* Secondary metrics */
.secondary-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.8rem;
}

.metric-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    color: var(--text-primary);
    font-weight: 600;
}

/* Insights cards */
.insights-cards-grid {
    margin-bottom: 2rem !important;
    margin-top: 1rem !important;
}

.insight-card {
    height: 100%;
    border: 1px solid var(--border-color) !important;
    border-radius: 16px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.insight-card .ui.card > .content > .header {
    color: var(--text-primary) !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.insight-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.insight-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.insight-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.insight-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.insight-value.highlight {
    color: var(--button-primary);
    background: rgba(67, 24, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.stat-card {
    background: linear-gradient(135deg, #2185d0 0%, #1678c2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    transition: transform 0.2s ease !important;
    margin-bottom: 1rem !important;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .statistic {
    color: white !important;
}

.charts-grid {
    margin-bottom: 2rem !important;
    margin-top: 1rem !important;
}

.charts-grid .column {
    padding: 0.75rem !important;
}

/* Enhanced grid spacing */
.ui.grid > .row > .column {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
}

.chart-card {
    height: 100%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 20px !important;
    padding: 1.5rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: var(--card-bg) !important;
    position: relative;
    overflow: hidden;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4318FF, #6C63FF, #00B5D8);
    opacity: 0.8;
}

.chart-container {
    margin-top: 1rem;
    padding: 0;
    background-color: transparent;
    border-radius: 12px;
    position: relative;
}

/* Enhanced chart styling */
.chart-container .recharts-surface {
    border-radius: 12px;
}

.chart-container .recharts-cartesian-grid-horizontal line {
    stroke: var(--border-color);
    stroke-opacity: 0.3;
    stroke-dasharray: 2 2;
}

.chart-container .recharts-cartesian-grid-vertical line {
    stroke: transparent;
}

/* Custom tooltip styling */
.custom-tooltip {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    padding: 12px 16px !important;
    color: var(--text-primary) !important;
}

.custom-tooltip .tooltip-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.custom-tooltip .tooltip-value {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.85rem;
}

/* Loading skeleton styles */
.loading-skeleton {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1rem;
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skeleton-line {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
}

.skeleton-title {
    height: 20px;
    width: 60%;
}

.skeleton-chart {
    height: 100px;
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 8px;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Enhanced button styling */
.refresh-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    padding: 0.8rem 1.5rem !important;
}

.refresh-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(67, 24, 255, 0.3) !important;
}

.refresh-button:active {
    transform: translateY(0) !important;
}

.refresh-button .icon {
    margin-right: 0.5rem !important;
}

/* Enhanced date controls */
.date-presets {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
}

.preset-button {
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    padding: 0.6rem 1.2rem !important;
}

.preset-button:hover {
    background: var(--button-primary) !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(67, 24, 255, 0.2) !important;
}

.preset-button:active {
    transform: translateY(0) !important;
}

.modern-date-input {
    background: var(--input-bg) !important;
    border: 2px solid var(--border-color) !important;
    border-radius: 8px !important;
    padding: 0.75rem !important;
    font-size: 0.95rem !important;
    color: var(--text-primary) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    width: 100% !important;
}

.modern-date-input:focus {
    border-color: var(--button-primary) !important;
    box-shadow: 0 0 0 3px rgba(67, 24, 255, 0.1) !important;
    outline: none !important;
}

.modern-date-input:hover {
    border-color: var(--button-primary) !important;
}

/* Enhanced form labels */
.ui.form .field > label {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
}

/* Controls card enhancement */
.dashboard-container .ui.card:first-child {
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--bg-secondary) 100%) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
}

.ui.card > .content > .header {
    color: var(--text-primary);
    font-size: 1.3em;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 700;
    gap: 12px;
    letter-spacing: -0.02em;
    line-height: 1.3;
}

/* Enhanced header styling for chart cards */
.chart-card .ui.card > .content > .header {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    position: relative;
}

.chart-card .ui.card > .content > .header::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #4318FF, #6C63FF);
    border-radius: 1px;
}

.stat-value {
    color: var(--button-primary);
    font-weight: bold;
    font-size: 1.1em;
    background: rgba(74, 158, 255, 0.1);
    padding: 4px 12px;
    border-radius: 8px;
    white-space: nowrap; /* 防止数值换行 */
    margin-left: 16px;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
        max-width: 100%;
    }

    .chart-container {
        padding: 0.75rem;
    }

    .charts-grid .column,
    .summary-cards-grid .column {
        padding: 0.5rem !important;
    }

    .summary-card-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .summary-icon-wrapper {
        width: 50px;
        height: 50px;
        margin: 0 auto;
    }

    .date-presets {
        margin-bottom: 1rem;
    }

    .preset-button {
        padding: 0.5rem 0.8rem !important;
        font-size: 0.85rem !important;
    }

    .ui.form .fields {
        flex-direction: column !important;
    }

    .ui.form .field {
        margin-bottom: 1rem !important;
    }

    .chart-card {
        padding: 1rem !important;
    }

    .ui.card > .content > .header {
        font-size: 1.1em;
    }

    .summary-card .ui.statistic > .value {
        font-size: 1.5rem !important;
    }

    /* Ensure large numbers fit on smaller screens */
    .summary-card .ui.statistic > .value {
        font-size: clamp(1.2rem, 4vw, 1.5rem) !important;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0.75rem;
    }

    .summary-card {
        margin-bottom: 0.75rem;
    }

    /* Further reduce font size for very small screens to prevent truncation */
    .summary-card .ui.statistic > .value {
        font-size: clamp(1rem, 3.5vw, 1.3rem) !important;
    }

    .chart-card {
        padding: 0.75rem !important;
    }

    .date-presets .ui.buttons {
        flex-direction: column;
        width: 100%;
    }

    .preset-button {
        width: 100% !important;
        margin-bottom: 0.5rem !important;
    }
}

/* Dark mode enhancements */
[data-theme="dark"] .summary-card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .summary-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .chart-card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .chart-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .loading-skeleton {
    background: var(--card-bg) !important;
}

[data-theme="dark"] .preset-button {
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .preset-button:hover {
    background: var(--button-primary) !important;
    color: white !important;
}

[data-theme="dark"] .modern-date-input {
    background: var(--input-bg) !important;
    border: 2px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .modern-date-input:focus {
    border-color: var(--button-primary) !important;
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2) !important;
}

/* Smooth transitions for all interactive elements */
* {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Enhanced focus states for accessibility */
.summary-card:focus-within,
.chart-card:focus-within {
    outline: 2px solid var(--button-primary);
    outline-offset: 2px;
}

/* Improved text contrast */
.ui.card > .content > .header {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .ui.card > .content > .header {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Date info message styling */
.date-info-message {
    margin-top: 1rem !important;
    border-radius: 8px !important;
    border: 1px solid var(--info-color) !important;
    background: rgba(23, 162, 184, 0.1) !important;
}

.date-info-message .header {
    color: var(--info-color) !important;
    font-weight: 600 !important;
}

.date-info-message p {
    color: var(--text-secondary) !important;
    margin: 0.5rem 0 0 0 !important;
}

[data-theme="dark"] .date-info-message {
    background: rgba(33, 150, 243, 0.15) !important;
    border: 1px solid var(--info-color) !important;
}

/* Model Efficiency Analysis */
.efficiency-analysis-card {
    margin-top: 2rem;
    border: 1px solid var(--border-color) !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
}

.efficiency-table-container {
    margin-top: 1rem;
    overflow-x: auto;
}

.efficiency-table {
    width: 100%;
    min-width: 600px;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.header-cell {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    text-align: left;
}

.table-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.table-row:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table-cell {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.model-name {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
}

.model-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--button-primary);
    color: white;
    border-radius: 50%;
    font-size: 0.8rem;
    font-weight: 700;
}

.efficiency-bar {
    position: relative;
    width: 100%;
    height: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    overflow: hidden;
}

.efficiency-fill {
    height: 100%;
    background: linear-gradient(90deg, #4318FF, #6C63FF);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.efficiency-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Dark mode support for efficiency table */
[data-theme="dark"] .efficiency-analysis-card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .table-row {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .table-row:hover {
    background: var(--bg-secondary) !important;
}

/* Cost Optimization Recommendations */
.optimization-recommendations-card {
    margin-top: 2rem;
    border: 1px solid var(--border-color) !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
}

.recommendations-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.recommendation-item.success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
}

.recommendation-item.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
}

.recommendation-item.info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
}

.recommendation-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recommendation-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.recommendation-item.success .recommendation-icon {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.recommendation-item.warning .recommendation-icon {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.recommendation-item.info .recommendation-icon {
    background: rgba(23, 162, 184, 0.2);
    color: var(--info-color);
}

.recommendation-content {
    flex: 1;
}

.recommendation-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.recommendation-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Dark mode support for recommendations */
[data-theme="dark"] .optimization-recommendations-card {
    background: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .recommendation-item.success {
    background: rgba(76, 175, 80, 0.15) !important;
}

[data-theme="dark"] .recommendation-item.warning {
    background: rgba(255, 152, 0, 0.15) !important;
}

[data-theme="dark"] .recommendation-item.info {
    background: rgba(33, 150, 243, 0.15) !important;
}

/* Additional styling for trend indicators in insights */
.insight-value.positive {
    color: var(--success-color) !important;
}

.insight-value.negative {
    color: var(--error-color) !important;
}

.insight-value.neutral {
    color: var(--warning-color) !important;
}

/* Real-time status indicator */
.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.real-time-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(67, 24, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(67, 24, 255, 0.2);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
}

.status-text {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes pulse-dot {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Dark mode support for status indicator */
[data-theme="dark"] .status-indicator {
    background: rgba(74, 158, 255, 0.15) !important;
    border: 1px solid rgba(74, 158, 255, 0.3) !important;
}

/* Responsive adjustments for controls header */
@media (max-width: 768px) {
    .controls-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .real-time-status {
        width: 100%;
        justify-content: center;
    }
}

/* 设置页面的 Tab 样式 */
.settings-tab {
    margin-top: 1rem !important;
    border-bottom: none !important;
}

.settings-tab .item {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
    padding: 0.8rem 1.2rem !important;
}

.settings-tab .active.item {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    border-color: var(--text-primary) !important;
}

.ui.tab.segment {
    border: none !important;
    box-shadow: none !important;
    padding: 1rem 0 !important;
}

/* Number tooltip styling */
.summary-card .ui.statistic > .value span[title] {
    transition: all 0.2s ease;
    border-bottom: 1px dotted currentColor;
}

.summary-card .ui.statistic > .value span[title]:hover {
    color: var(--button-primary) !important;
    border-bottom-style: solid;
    transform: translateY(-1px);
}

/* Table cell tooltip styling */
.table-cell span[title] {
    transition: all 0.2s ease;
}

.table-cell span[title]:hover {
    color: var(--button-primary);
    transform: translateY(-1px);
}

/* Secondary metric tooltip styling */
.secondary-metric span[title] {
    transition: all 0.2s ease;
}

.secondary-metric span[title]:hover {
    color: var(--button-primary);
    transform: translateY(-1px);
}
