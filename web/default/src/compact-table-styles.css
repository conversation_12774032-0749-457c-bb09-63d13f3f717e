/* Compact Table Styles - Consistent with redemption page */

/* Clean empty cells - hide text for empty values */
.ui.table tbody td:empty::before {
  content: '';
  display: none;
}

/* Sortable header cursor */
.sortable-header {
  cursor: pointer !important;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background-color: var(--menu-hover, rgba(0, 0, 0, 0.05)) !important;
}

/* Timestamp code styling */
.timestamp-code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 2px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.timestamp-code:hover {
  background: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .timestamp-code {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

[data-theme="dark"] .timestamp-code:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Compact form styling */
.ui.form {
  margin-bottom: 1rem !important;
}

.ui.form .ui.input {
  margin-bottom: 0 !important;
}

/* Compact table styling */
.ui.table.basic.very.compact.small {
  border: 1px solid var(--border-color, #e0e0e0) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  margin-top: 0 !important;
  margin-bottom: 2rem !important;
  overflow: hidden !important;
}

.ui.table.basic.very.compact.small thead th {
  background-color: var(--bg-secondary, #f8f9fa) !important;
  color: var(--text-primary, #333) !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
  border-bottom: 2px solid var(--border-color, #e0e0e0) !important;
}

.ui.table.basic.very.compact.small tbody tr {
  transition: background-color 0.2s ease !important;
}

.ui.table.basic.very.compact.small tbody tr:hover {
  background-color: var(--menu-hover, rgba(0, 0, 0, 0.02)) !important;
}

.ui.table.basic.very.compact.small tbody td {
  padding: 8px !important;
  border-top: 1px solid var(--border-color, #e0e0e0) !important;
  vertical-align: middle !important;
}

.ui.table.basic.very.compact.small tfoot th {
  background-color: var(--bg-secondary, #f8f9fa) !important;
  border-top: 2px solid var(--border-color, #e0e0e0) !important;
  padding: 12px 8px !important;
}

/* Dark theme support */
[data-theme="dark"] .ui.table.basic.very.compact.small {
  background-color: var(--card-bg) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.table.basic.very.compact.small thead th {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .ui.table.basic.very.compact.small tbody tr:hover {
  background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.table.basic.very.compact.small tbody td {
  border-top-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.table.basic.very.compact.small tfoot th {
  background-color: var(--bg-secondary) !important;
  border-top-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Button spacing in action cells */
.ui.table tbody td div {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.ui.table tbody td .ui.button.tiny {
  font-size: 0.75rem !important;
  padding: 0.5em 0.75em !important;
  margin: 0 !important;
}

/* Label adjustments for compact display */
.ui.label.basic.mini {
  font-size: 0.7rem !important;
  padding: 0.3em 0.5em !important;
}

.ui.label.circular {
  min-width: 2em !important;
  text-align: center !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .ui.table.basic.very.compact.small {
    font-size: 0.9rem !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border-left: none !important;
    border-right: none !important;
  }

  .ui.table.basic.very.compact.small thead th,
  .ui.table.basic.very.compact.small tbody td,
  .ui.table.basic.very.compact.small tfoot th {
    padding: 6px 4px !important;
    font-size: 0.85rem !important;
  }

  .ui.table tbody td div {
    flex-direction: column;
    gap: 2px;
  }

  .ui.table tbody td .ui.button.tiny {
    width: 100%;
    text-align: center;
  }
}

/* Tablet responsive adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
  .ui.table.basic.very.compact.small {
    font-size: 0.95rem !important;
  }

  .ui.table.basic.very.compact.small thead th,
  .ui.table.basic.very.compact.small tbody td,
  .ui.table.basic.very.compact.small tfoot th {
    padding: 8px 6px !important;
  }
}

/* Ensure consistent overflow handling */
.ui.table tbody td {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ui.table tbody td div {
  white-space: normal;
  overflow: visible;
}

/* Special styling for specific cell types */
.ui.table tbody td:last-child {
  white-space: normal;
  max-width: none;
}

.ui.table tbody td:first-child {
  max-width: 80px;
}

/* Status and type labels */
.ui.label {
  white-space: nowrap !important;
}

.ui.label .icon {
  margin-right: 0.3em !important;
}

/* Search form spacing */
.ui.form {
  margin-bottom: 1.5rem !important;
}

/* Footer button spacing */
.ui.table tfoot .ui.button {
  margin-right: 0.5rem !important;
}
