/* Global Table Overflow Fixes */

/* Base table container fixes */
.ui.table,
table {
  width: 100%;
  table-layout: auto;
  border-collapse: separate;
  border-spacing: 0;
}

/* Table wrapper for overflow handling */
.table-wrapper,
.data-table-wrapper,
.channels-table-container,
.tokens-table-container,
.logs-table-container,
.users-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

/* Ensure tables have minimum width to prevent cramping */
.table-wrapper .ui.table,
.data-table-wrapper .ui.table,
.channels-table-container .ui.table,
.tokens-table-container .ui.table,
.logs-table-container .ui.table,
.users-table-container .ui.table {
  min-width: 800px;
  width: 100%;
}

/* Mobile responsive table fixes */
@media (max-width: 768px) {
  .table-wrapper,
  .data-table-wrapper,
  .channels-table-container,
  .tokens-table-container,
  .logs-table-container,
  .users-table-container {
    margin: 0;
    padding: 0;
    border-radius: 0;
  }

  .table-wrapper .ui.table,
  .data-table-wrapper .ui.table,
  .channels-table-container .ui.table,
  .tokens-table-container .ui.table,
  .logs-table-container .ui.table,
  .users-table-container .ui.table {
    min-width: 600px;
    font-size: 12px;
    border-radius: 0;
  }

  .table-wrapper .ui.table th,
  .table-wrapper .ui.table td,
  .data-table-wrapper .ui.table th,
  .data-table-wrapper .ui.table td,
  .channels-table-container .ui.table th,
  .channels-table-container .ui.table td,
  .tokens-table-container .ui.table th,
  .tokens-table-container .ui.table td,
  .logs-table-container .ui.table th,
  .logs-table-container .ui.table td,
  .users-table-container .ui.table th,
  .users-table-container .ui.table td {
    padding: 8px 4px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
}

/* Tablet responsive adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
  .table-wrapper .ui.table,
  .data-table-wrapper .ui.table,
  .channels-table-container .ui.table,
  .tokens-table-container .ui.table,
  .logs-table-container .ui.table,
  .users-table-container .ui.table {
    min-width: 700px;
    font-size: 13px;
  }

  .table-wrapper .ui.table th,
  .table-wrapper .ui.table td,
  .data-table-wrapper .ui.table th,
  .data-table-wrapper .ui.table td,
  .channels-table-container .ui.table th,
  .channels-table-container .ui.table td,
  .tokens-table-container .ui.table th,
  .tokens-table-container .ui.table td,
  .logs-table-container .ui.table th,
  .logs-table-container .ui.table td,
  .users-table-container .ui.table th,
  .users-table-container .ui.table td {
    padding: 10px 6px !important;
    max-width: 200px;
  }
}

/* Prevent tables from breaking container boundaries */
body .ui.container .ui.table,
.ui.container .ui.table {
  max-width: 100%;
  overflow: visible;
}

/* Ensure Semantic UI tables don't override our overflow fixes */
.ui.table tbody tr,
.ui.table thead tr {
  width: 100%;
}

.ui.table tbody tr td,
.ui.table thead tr th {
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Horizontal scroll styling */
.table-wrapper::-webkit-scrollbar,
.data-table-wrapper::-webkit-scrollbar,
.channels-table-container::-webkit-scrollbar,
.tokens-table-container::-webkit-scrollbar,
.logs-table-container::-webkit-scrollbar,
.users-table-container::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track,
.data-table-wrapper::-webkit-scrollbar-track,
.channels-table-container::-webkit-scrollbar-track,
.tokens-table-container::-webkit-scrollbar-track,
.logs-table-container::-webkit-scrollbar-track,
.users-table-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb,
.data-table-wrapper::-webkit-scrollbar-thumb,
.channels-table-container::-webkit-scrollbar-thumb,
.tokens-table-container::-webkit-scrollbar-thumb,
.logs-table-container::-webkit-scrollbar-thumb,
.users-table-container::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover,
.data-table-wrapper::-webkit-scrollbar-thumb:hover,
.channels-table-container::-webkit-scrollbar-thumb:hover,
.tokens-table-container::-webkit-scrollbar-thumb:hover,
.logs-table-container::-webkit-scrollbar-thumb:hover,
.users-table-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}

/* Dark theme scrollbar support */
[data-theme="dark"] .table-wrapper::-webkit-scrollbar-track,
[data-theme="dark"] .data-table-wrapper::-webkit-scrollbar-track,
[data-theme="dark"] .channels-table-container::-webkit-scrollbar-track,
[data-theme="dark"] .tokens-table-container::-webkit-scrollbar-track,
[data-theme="dark"] .logs-table-container::-webkit-scrollbar-track,
[data-theme="dark"] .users-table-container::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] .table-wrapper::-webkit-scrollbar-thumb,
[data-theme="dark"] .data-table-wrapper::-webkit-scrollbar-thumb,
[data-theme="dark"] .channels-table-container::-webkit-scrollbar-thumb,
[data-theme="dark"] .tokens-table-container::-webkit-scrollbar-thumb,
[data-theme="dark"] .logs-table-container::-webkit-scrollbar-thumb,
[data-theme="dark"] .users-table-container::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
}
