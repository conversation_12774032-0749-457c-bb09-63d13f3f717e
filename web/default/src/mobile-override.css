/* ==============================|| MOBILE OVERRIDE FRAMEWORK ||============================== */
/* MAXIMUM SPECIFICITY CSS TO OVERRIDE ALL EXISTING STYLES */
/* This file uses the highest possible CSS specificity to ensure mobile styles are applied */

@media screen and (max-width: 768px) {

  /* ==============================|| CRITICAL LAYOUT RESET ||============================== */

  /* Maintain proper body spacing for mobile */
  html body,
  body {
    padding-top: 60px !important;
    /* Keep header space */
    margin-top: 0 !important;
  }

  /* Override root container */
  html #root,
  #root {
    min-height: calc(100vh - 60px) !important;
    padding-top: 0 !important;
    margin-top: 0 !important;
  }

  /* Override main content container */
  html body .main-content,
  body .main-content,
  .main-content {
    padding: 4px !important;
    /* Minimal padding */
    margin: 0 !important;
    min-height: calc(100vh - 60px) !important;
  }

  /* Override Semantic UI container */
  html body .ui.container,
  body .ui.container,
  .ui.container {
    margin: 0 !important;
    padding: 0 !important;
    /* Remove all padding */
    width: 100% !important;
    max-width: 100% !important;
  }

  /* ==============================|| NAVIGATION MENU FIXES ||============================== */

  /* Fix navigation menu positioning only - NOT content headers */
  html body .ui.menu,
  body .ui.menu,
  .ui.menu {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }

  /* Ensure content headers are NOT sticky */
  html body .ui.header,
  body .ui.header,
  .ui.header,
  html body h1.ui.header,
  body h1.ui.header,
  h1.ui.header,
  html body h2.ui.header,
  body h2.ui.header,
  h2.ui.header,
  html body h3.ui.header,
  body h3.ui.header,
  h3.ui.header {
    position: static !important;
    background: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    z-index: auto !important;
  }

  /* ==============================|| TABLE COMPLETE OVERRIDE ||============================== */

  /* Hide table headers - MAXIMUM SPECIFICITY */
  html body .ui.table thead,
  body .ui.table thead,
  .ui.table thead,
  html body table thead,
  body table thead,
  table thead {
    display: none !important;
  }

  /* Table container reset */
  html body .ui.table,
  body .ui.table,
  .ui.table,
  html body table,
  body table,
  table {
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    background: transparent !important;
    width: 100% !important;
  }

  /* Card-based row layout - MAXIMUM SPECIFICITY */
  html body .ui.table tbody tr,
  body .ui.table tbody tr,
  .ui.table tbody tr,
  html body table tbody tr,
  body table tbody tr,
  table tbody tr {
    display: block !important;
    background-color: var(--card-bg) !important;
    border: none !important;
    /* Remove all borders */
    border-radius: 4px !important;
    margin: 2px 0 !important;
    /* Minimal vertical margin only */
    padding: 8px !important;
    /* Reduced padding */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    width: 100% !important;
    /* Full width */
  }

  html body .ui.table tbody tr:hover,
  body .ui.table tbody tr:hover,
  .ui.table tbody tr:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* Cell styling - MAXIMUM SPECIFICITY */
  html body .ui.table tbody tr td,
  body .ui.table tbody tr td,
  .ui.table tbody tr td,
  html body table tbody tr td,
  body table tbody tr td,
  table tbody tr td {
    display: flex !important;
    align-items: flex-start !important;
    padding: 8px 0 !important;
    border: none !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.5 !important;
    min-height: auto !important;
    width: 100% !important;
    background: transparent !important;
  }

  /* Data labels using CSS content */
  html body .ui.table tbody tr td:before,
  body .ui.table tbody tr td:before,
  .ui.table tbody tr td:before {
    content: attr(data-label) !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    font-size: 14px !important;
    min-width: 80px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
    display: inline-block !important;
  }

  html body .ui.table tbody tr td>*,
  body .ui.table tbody tr td>*,
  .ui.table tbody tr td>* {
    flex: 1 !important;
    margin: 0 !important;
  }

  /* ==============================|| COMPLETE BORDER REMOVAL ||============================== */

  /* Remove ALL borders with maximum specificity */
  html body .ui.table,
  html body .ui.table *,
  body .ui.table,
  body .ui.table *,
  .ui.table,
  .ui.table *,
  html body table,
  html body table *,
  body table,
  body table *,
  table,
  table * {
    border: none !important;
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    border-collapse: separate !important;
  }

  /* ==============================|| FORM OPTIMIZATION ||============================== */

  /* Form container */
  html body .ui.form,
  body .ui.form,
  .ui.form {
    padding: 8px !important;
    /* Reduced padding */
    margin: 4px 0 !important;
    /* Minimal margin */
    background-color: var(--bg-primary) !important;
    border: none !important;
    /* Remove borders */
  }

  /* Form fields */
  html body .ui.form .field,
  body .ui.form .field,
  .ui.form .field {
    width: 100% !important;
    margin-bottom: 16px !important;
  }

  /* Form inputs */
  html body .ui.form input,
  html body .ui.form .ui.input input,
  body .ui.form input,
  body .ui.form .ui.input input,
  .ui.form input,
  .ui.form .ui.input input {
    width: 100% !important;
    min-height: 44px !important;
    font-size: 16px !important;
    padding: 12px 16px !important;
    border-radius: 6px !important;
    border: 1px solid var(--input-border) !important;
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
  }

  /* Buttons */
  html body .ui.button,
  body .ui.button,
  .ui.button {
    width: 100% !important;
    min-height: 44px !important;
    margin: 8px 0 !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
  }

  /* ==============================|| COMPONENT OVERRIDES ||============================== */

  /* Labels */
  html body .ui.label,
  body .ui.label,
  .ui.label {
    margin: 2px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid transparent !important;
  }

  /* Pagination */
  html body .ui.pagination,
  body .ui.pagination,
  .ui.pagination {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 8px !important;
    margin: 16px 0 !important;
    padding: 0 16px !important;
  }

  html body .ui.pagination .item,
  body .ui.pagination .item,
  .ui.pagination .item {
    min-width: 44px !important;
    height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border: 1px solid #dee2e6 !important;
    background-color: #ffffff !important;
    color: #212529 !important;
  }

  /* ==============================|| DARK THEME SUPPORT ||============================== */

  /* Dark theme navigation menu */
  [data-theme="dark"] html body .ui.menu,
  [data-theme="dark"] body .ui.menu,
  [data-theme="dark"] .ui.menu {
    background-color: rgba(30, 30, 30, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }

  /* Ensure content headers remain normal in dark theme */
  [data-theme="dark"] html body .ui.header,
  [data-theme="dark"] body .ui.header,
  [data-theme="dark"] .ui.header {
    background: transparent !important;
    color: var(--text-primary) !important;
  }

  [data-theme="dark"] html body .ui.table tbody tr,
  [data-theme="dark"] body .ui.table tbody tr,
  [data-theme="dark"] .ui.table tbody tr {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
  }

  [data-theme="dark"] html body .ui.form,
  [data-theme="dark"] body .ui.form,
  [data-theme="dark"] .ui.form {
    background-color: var(--bg-primary) !important;
  }

  [data-theme="dark"] html body .ui.form input,
  [data-theme="dark"] body .ui.form input,
  [data-theme="dark"] .ui.form input {
    background-color: var(--input-bg) !important;
    border-color: var(--input-border) !important;
    color: var(--text-primary) !important;
  }
}
