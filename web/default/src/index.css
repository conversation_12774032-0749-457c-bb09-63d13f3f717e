/* Responsive card-style table for mobile (BaseTable, management tables) */
@media (max-width: 700px) {

    .ui.table,
    .ui.table thead,
    .ui.table tfoot {
        display: block !important;
        width: 100%;
    }

    .ui.table thead,
    .ui.table tfoot {
        display: none !important;
    }

    .ui.table tbody {
        display: block !important;
        width: 100%;
    }

    .ui.table tr {
        display: block !important;
        margin-bottom: 1.5em;
        background: var(--table-row-bg, #fff);
        border-radius: 8px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        padding: 1em 0.5em;
        border: 1px solid #eee;
    }

    .ui.table td,
    .ui.table th {
        display: flex !important;
        width: 100%;
        align-items: center;
        border: none !important;
        padding: 0.5em 0.5em;
        font-size: 1em;
        background: none !important;
        box-shadow: none !important;
        word-break: break-word;
    }

    .ui.table td:before,
    .ui.table th:before {
        content: attr(data-label) ": ";
        flex: 0 0 40%;
        font-weight: bold;
        color: #888;
        margin-right: 0.5em;
        text-align: left;
        min-width: 90px;
        max-width: 50vw;
        white-space: pre-line;
    }

    .ui.table td>*,
    .ui.table th>* {
        flex: 1 1 60%;
        min-width: 0;
        word-break: break-word;
    }

    .ui.table .table-footer-content {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5em;
    }

    .ui.table .table-footer-buttons {
        flex-direction: column;
        gap: 0.5em;
    }

    .ui.table .table-pagination-container {
        margin-top: 0.5em;
        width: 100%;
        display: flex;
        justify-content: center;
    }
}

/* CSS Variables for Theme Support */
:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --menu-bg: #ffffff;
    --menu-hover: #f8f9fa;
    --menu-active: #e9ecef;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --button-primary: #007bff;
    --button-secondary: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --info-color: #17a2b8;
}

[data-theme="dark"] {
    /* Dark theme colors - More refined palette */
    --bg-primary: #1e1e1e;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #363636;
    --text-primary: #e4e4e4;
    --text-secondary: #b8b8b8;
    --text-muted: #888888;
    --border-color: #3a3a3a;
    --shadow-color: rgba(0, 0, 0, 0.4);
    --menu-bg: #2a2a2a;
    --menu-hover: #363636;
    --menu-active: #404040;
    --card-bg: #2a2a2a;
    --input-bg: #363636;
    --input-border: #4a4a4a;
    --button-primary: #4a9eff;
    --button-secondary: #5a5a5a;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
}

body {
    margin: 0;
    padding-top: 60px;
    /* Increased from 55px to account for header height */
    overflow-y: scroll;
    font-family: Lato, 'Helvetica Neue', Arial, Helvetica, "Microsoft YaHei", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scrollbar-width: none;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    min-height: 100vh;
}

html {
    height: 100%;
}

#root {
    min-height: calc(100vh - 60px);
    /* Updated to match body padding-top */
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
}

body::-webkit-scrollbar {
    display: none;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.main-content {
    padding: 8px;
    /* Increased from 4px for better spacing */
    background-color: var(--bg-primary);
    flex: 1;
    min-height: calc(100vh - 60px - 60px);
    /* Updated header height */
}

/* Footer positioning */
.ui.vertical.segment {
    background-color: var(--bg-secondary);
    margin-top: auto;
    padding: 1rem 0;
}

[data-theme="dark"] .ui.vertical.segment {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Dashboard and settings container */
.dashboard-container {
    background-color: var(--bg-primary);
    min-height: calc(100vh - 60px - 60px);
    /* Updated header height */
    padding: 1rem;
}

[data-theme="dark"] .dashboard-container {
    background-color: var(--bg-primary);
}

/* Settings page specific styling */
[data-theme="dark"] .chart-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

[data-theme="dark"] .chart-card .content {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

[data-theme="dark"] .chart-card .header {
    color: var(--text-primary);
    background-color: var(--card-bg);
}

/* Settings tabs styling */
[data-theme="dark"] .settings-tab .item {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .settings-tab .active.item {
    color: var(--text-primary);
    background-color: var(--card-bg);
    border-color: var(--button-primary);
    border-bottom-color: var(--card-bg);
}

[data-theme="dark"] .settings-tab .item:hover {
    background-color: var(--menu-hover);
    color: var(--text-primary);
}

/* Ensure all page backgrounds are properly themed */
[data-theme="dark"] .ui.container {
    background-color: transparent;
}

[data-theme="dark"] .ui.grid {
    background-color: transparent;
}

[data-theme="dark"] .ui.grid>.row {
    background-color: transparent;
}

[data-theme="dark"] .ui.grid>.column {
    background-color: transparent;
}

/* Fix any remaining white backgrounds */
[data-theme="dark"] * {
    background-color: inherit;
}

[data-theme="dark"] .ui.card,
[data-theme="dark"] .ui.cards>.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .ui.card>.content,
[data-theme="dark"] .ui.cards>.card>.content {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border-color: var(--border-color);
}

/* Footer text color fix */
[data-theme="dark"] .custom-footer {
    color: var(--text-secondary);
}

[data-theme="dark"] .custom-footer a {
    color: var(--button-primary);
}

[data-theme="dark"] .custom-footer a:hover {
    color: var(--text-primary);
}

/* Theme-aware Semantic UI overrides */
[data-theme="dark"] .ui.menu {
    background-color: var(--menu-bg) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 1px 3px var(--shadow-color) !important;
}

[data-theme="dark"] .ui.menu .item {
    color: var(--text-secondary) !important;
    transition: all 0.2s ease;
}

[data-theme="dark"] .ui.menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.menu .active.item {
    background-color: var(--menu-active) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.container {
    background-color: transparent;
}

[data-theme="dark"] .ui.segment {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 2px 8px var(--shadow-color);
}

[data-theme="dark"] .ui.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 2px 8px var(--shadow-color);
}

[data-theme="dark"] .ui.table {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table thead th {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.table tbody tr {
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.table tbody tr:hover {
    background-color: var(--menu-hover);
}

[data-theme="dark"] .ui.table tbody td {
    border-top: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.input input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.input input:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

/* Text areas and form inputs */
[data-theme="dark"] textarea {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] textarea:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
    outline: none;
}

[data-theme="dark"] .ui.form textarea {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form textarea:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.form input[type="text"],
[data-theme="dark"] .ui.form input[type="email"],
[data-theme="dark"] .ui.form input[type="password"],
[data-theme="dark"] .ui.form input[type="number"],
[data-theme="dark"] .ui.form input[type="url"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form input[type="text"]:focus,
[data-theme="dark"] .ui.form input[type="email"]:focus,
[data-theme="dark"] .ui.form input[type="password"]:focus,
[data-theme="dark"] .ui.form input[type="number"]:focus,
[data-theme="dark"] .ui.form input[type="url"]:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.dropdown .menu {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color);
}

[data-theme="dark"] .ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    border: none;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover);
}

[data-theme="dark"] .ui.dropdown .text {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.button {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
    transition: all 0.2s ease;
}

[data-theme="dark"] .ui.button:hover {
    background-color: var(--bg-tertiary);
}

[data-theme="dark"] .ui.button.primary {
    background-color: var(--button-primary) !important;
    color: white !important;
    border-color: var(--button-primary) !important;
}

[data-theme="dark"] .ui.button.primary:hover {
    background-color: rgba(74, 158, 255, 0.8);
}

[data-theme="dark"] .ui.button.green {
    background-color: var(--success-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.button.red {
    background-color: var(--error-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.button.orange {
    background-color: var(--warning-color) !important;
    color: white !important;
}

[data-theme="dark"] .ui.modal {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.modal .header {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.modal .content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

/* Dark mode label styles - comprehensive coverage for all colors */
[data-theme="dark"] .ui.label {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

/* Basic labels with dark-friendly colors */
[data-theme="dark"] .ui.label.basic {
    background-color: rgba(255, 255, 255, 0.08) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Color-specific label styles for dark mode */
[data-theme="dark"] .ui.label.red,
[data-theme="dark"] .ui.label.basic.red {
    background-color: rgba(244, 67, 54, 0.15) !important;
    color: #ff8a80;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

[data-theme="dark"] .ui.label.orange,
[data-theme="dark"] .ui.label.basic.orange {
    background-color: rgba(255, 152, 0, 0.15) !important;
    color: #ffb74d;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

[data-theme="dark"] .ui.label.yellow,
[data-theme="dark"] .ui.label.basic.yellow {
    background-color: rgba(255, 235, 59, 0.15) !important;
    color: #fff176;
    border: 1px solid rgba(255, 235, 59, 0.3);
}

[data-theme="dark"] .ui.label.olive,
[data-theme="dark"] .ui.label.basic.olive {
    background-color: rgba(139, 195, 74, 0.15) !important;
    color: #aed581;
    border: 1px solid rgba(139, 195, 74, 0.3);
}

[data-theme="dark"] .ui.label.green,
[data-theme="dark"] .ui.label.basic.green {
    background-color: rgba(76, 175, 80, 0.15) !important;
    color: #81c784;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

[data-theme="dark"] .ui.label.teal,
[data-theme="dark"] .ui.label.basic.teal {
    background-color: rgba(0, 150, 136, 0.15) !important;
    color: #4db6ac;
    border: 1px solid rgba(0, 150, 136, 0.3);
}

[data-theme="dark"] .ui.label.blue,
[data-theme="dark"] .ui.label.basic.blue {
    background-color: rgba(33, 150, 243, 0.15) !important;
    color: #64b5f6;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

[data-theme="dark"] .ui.label.violet,
[data-theme="dark"] .ui.label.basic.violet {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #ba68c8;
    border: 1px solid rgba(156, 39, 176, 0.3);
}

[data-theme="dark"] .ui.label.purple,
[data-theme="dark"] .ui.label.basic.purple {
    background-color: rgba(103, 58, 183, 0.15) !important;
    color: #9575cd;
    border: 1px solid rgba(103, 58, 183, 0.3);
}

[data-theme="dark"] .ui.label.pink,
[data-theme="dark"] .ui.label.basic.pink {
    background-color: rgba(233, 30, 99, 0.15) !important;
    color: #f06292;
    border: 1px solid rgba(233, 30, 99, 0.3);
}

[data-theme="dark"] .ui.label.brown,
[data-theme="dark"] .ui.label.basic.brown {
    background-color: rgba(121, 85, 72, 0.15) !important;
    color: #a1887f;
    border: 1px solid rgba(121, 85, 72, 0.3);
}

[data-theme="dark"] .ui.label.grey,
[data-theme="dark"] .ui.label.basic.grey {
    background-color: rgba(158, 158, 158, 0.15) !important;
    color: #bdbdbd;
    border: 1px solid rgba(158, 158, 158, 0.3);
}

[data-theme="dark"] .ui.label.black,
[data-theme="dark"] .ui.label.basic.black {
    background-color: rgba(97, 97, 97, 0.15) !important;
    color: #9e9e9e;
    border: 1px solid rgba(97, 97, 97, 0.3);
}

/* Theme toggle button styles */
.theme-toggle-button {
    background: none !important;
    border: none !important;
    padding: 8px !important;
    margin: 0 8px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle-button:hover {
    background-color: var(--menu-hover) !important;
    transform: scale(1.05);
}

.theme-toggle-button .icon {
    font-size: 18px;
    color: var(--text-secondary) !important;
    margin: 0;
    transition: color 0.2s ease;
}

.theme-toggle-button:hover .icon {
    color: var(--text-primary) !important;
}

/* Additional dark theme improvements */
[data-theme="dark"] .ui.search .results {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color);
}

[data-theme="dark"] .ui.search .result {
    background-color: var(--menu-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.search .result:hover {
    background-color: var(--menu-hover);
}

[data-theme="dark"] .ui.form .field>label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.checkbox label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.checkbox input:checked~.box:before,
[data-theme="dark"] .ui.checkbox input:checked~label:before {
    background-color: var(--button-primary) !important;
    border-color: var(--button-primary) !important;
}

/* Legacy dark theme pagination rules - less specific to avoid conflicts */
[data-theme="dark"] .ui.pagination .item:not(.pagination-container .item) {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.pagination .active.item:not(.pagination-container .item) {
    background-color: var(--button-primary) !important;
    color: white !important;
}

[data-theme="dark"] .ui.pagination .item:hover:not(.pagination-container .item) {
    background-color: var(--menu-hover) !important;
}

/* Toast notifications dark theme */
[data-theme="dark"] .Toastify__toast {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .Toastify__toast--success {
    background-color: var(--success-color) !important;
}

[data-theme="dark"] .Toastify__toast--error {
    background-color: var(--error-color) !important;
}

[data-theme="dark"] .Toastify__toast--warning {
    background-color: var(--warning-color) !important;
}

[data-theme="dark"] .Toastify__toast--info {
    background-color: var(--info-color) !important;
}

/* Code editors and JSON formatters */
[data-theme="dark"] .ui.form .field textarea,
[data-theme="dark"] .ui.form .field input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form .field textarea:focus,
[data-theme="dark"] .ui.form .field input:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
}

/* Code blocks and pre elements */
[data-theme="dark"] pre {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

[data-theme="dark"] code {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

/* JSON editor specific */
[data-theme="dark"] .json-editor,
[data-theme="dark"] .json-formatter {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border);
}

/* Generic white backgrounds that might appear */
[data-theme="dark"] .ui.form .field>.ui.input,
[data-theme="dark"] .ui.form .field>.ui.dropdown {
    background-color: var(--input-bg) !important;
}

[data-theme="dark"] .ui.form .field>.ui.input input {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
}

/* Tab content areas */
[data-theme="dark"] .ui.tab.segment {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.tabular.menu .item {
    background-color: var(--bg-secondary) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.tabular.menu .active.item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom-color: var(--card-bg);
}

/* Message boxes and info panels */
[data-theme="dark"] .ui.message {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .ui.message.info {
    background-color: rgba(59, 130, 246, 0.1) !important;
    border-color: var(--info-color);
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: var(--warning-color);
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.error {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: var(--error-color);
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.message.success {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: var(--success-color);
    color: var(--text-primary) !important;
}

/* Additional form elements and containers */
[data-theme="dark"] .ui.form .field {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .text {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu {
    background-color: var(--menu-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu .item {
    background-color: var(--menu-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form .field .ui.selection.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
}

/* Content areas and panels */
[data-theme="dark"] .ui.grid>.column {
    background-color: transparent !important;
}

[data-theme="dark"] .ui.container>.ui.grid {
    background-color: transparent !important;
}

/* Specific overrides for any remaining white backgrounds */
[data-theme="dark"] * {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

[data-theme="dark"] *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] *::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] *::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] *::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

/* Ensure all text inputs have dark styling */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--button-primary) !important;
    background-color: var(--input-bg) !important;
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Logo and header text styling for dark mode */
[data-theme="dark"] .ui.menu .item div {
    color: var(--text-primary) !important;
}

/* Homepage content text styling */
[data-theme="dark"] .ui.header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card .header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card .description {
    color: var(--text-secondary);
}

/* Ensure all paragraph text is visible in dark mode */
[data-theme="dark"] p {
    color: var(--text-primary) !important;
}

[data-theme="dark"] span {
    color: inherit;
}

/* Links in dark mode */
[data-theme="dark"] a {
    color: var(--button-primary) !important;
}

[data-theme="dark"] a:hover {
    color: var(--text-primary) !important;
}

/* Chart and tooltip styling for dark mode */
[data-theme="dark"] .recharts-tooltip-wrapper {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 4px;
    box-shadow: 0 2px 8px var(--shadow-color);
}

[data-theme="dark"] .recharts-default-tooltip {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .recharts-tooltip-label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .recharts-tooltip-item {
    color: var(--text-primary) !important;
}

/* Dashboard specific styling */
[data-theme="dark"] .dashboard-container .stat-value {
    background: rgba(74, 158, 255, 0.15) !important;
    color: var(--button-primary) !important;
}

/* Ensure chart backgrounds are properly themed */
[data-theme="dark"] .recharts-surface {
    background-color: transparent;
}

[data-theme="dark"] .recharts-cartesian-grid line {
    stroke: var(--border-color);
    opacity: 0.3;
}

[data-theme="dark"] .recharts-text {
    fill: var(--text-secondary);
}

/* Theme dropdown styling - Enhanced for better visibility with higher specificity */
.ui.menu .theme-dropdown.ui.dropdown .menu,
.theme-dropdown.ui.dropdown .menu {
    min-width: 140px !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color) !important;
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item:last-child,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item:last-child {
    border-bottom: none;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item:hover,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item.active,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
    font-weight: 600;
}

[data-theme="dark"] .ui.menu .theme-dropdown.ui.dropdown .menu .item.active:hover,
[data-theme="dark"] .theme-dropdown.ui.dropdown .menu .item.active:hover {
    background-color: rgba(74, 158, 255, 0.8) !important;
    color: white !important;
}

/* Light mode theme dropdown styling for better contrast */
.ui.menu .theme-dropdown.ui.dropdown .menu .item,
.theme-dropdown.ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item:last-child,
.theme-dropdown.ui.dropdown .menu .item:last-child {
    border-bottom: none;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item:hover,
.theme-dropdown.ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item.active,
.theme-dropdown.ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
    font-weight: 600;
}

.ui.menu .theme-dropdown.ui.dropdown .menu .item.active:hover,
.theme-dropdown.ui.dropdown .menu .item.active:hover {
    background-color: rgba(0, 123, 255, 0.8) !important;
    color: white !important;
}

/* Ensure theme dropdown trigger is properly styled */
[data-theme="dark"] .ui.dropdown>.dropdown.icon {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .ui.dropdown>.text {
    color: var(--text-secondary) !important;
}

/* Additional fallback styling for theme dropdown in dark mode */
[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item:hover,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown.theme-dropdown .menu .item.active,
[data-theme="dark"] .ui.menu .ui.dropdown.theme-dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

/* Force override for Semantic UI dropdown menu in dark mode */
[data-theme="dark"] .ui.dropdown .menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

/* Better styling for all dropdown menus in dark mode */
[data-theme="dark"] .ui.dropdown .menu .item {
    color: var(--text-primary) !important;
    background-color: var(--menu-bg) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.dropdown .menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
}

.small-icon .icon {
    font-size: 1em;
}

.custom-footer {
    font-size: 1.1em;
}

@media only screen and (max-width: 600px) {
    .hide-on-mobile {
        display: none !important;
    }
}

/* Progressive header disclosure styles */
.header-more-dropdown .dropdown.menu {
    min-width: 180px;
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.header-more-dropdown .dropdown.item {
    padding: 8px 16px;
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    transition: all 0.2s ease;
}

.header-more-dropdown .dropdown.item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

.header-more-dropdown .dropdown.item:last-child {
    border-bottom: none;
}

/* Dark theme support for header more dropdown */
[data-theme="dark"] .header-more-dropdown .dropdown.menu {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .header-more-dropdown .dropdown.item {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .header-more-dropdown .dropdown.item:hover {
    background-color: var(--menu-hover) !important;
    color: var(--text-primary) !important;
}

/* ==============================|| LOG LABELS DARK MODE FIX ||============================== */

/* Fix bright colored labels in logs for better dark mode readability */
[data-theme="dark"] .ui.label {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Specific color adjustments for log type labels in dark mode */
[data-theme="dark"] .ui.label.green {
    background-color: rgba(33, 186, 69, 0.2) !important;
    color: #4ade80;
    border: 1px solid rgba(33, 186, 69, 0.3);
}

[data-theme="dark"] .ui.label.olive {
    background-color: rgba(181, 204, 24, 0.2) !important;
    color: #a3e635;
    border: 1px solid rgba(181, 204, 24, 0.3);
}

[data-theme="dark"] .ui.label.orange {
    background-color: rgba(242, 113, 28, 0.2) !important;
    color: #fb923c;
    border: 1px solid rgba(242, 113, 28, 0.3);
}

[data-theme="dark"] .ui.label.purple {
    background-color: rgba(163, 73, 164, 0.2) !important;
    color: #c084fc;
    border: 1px solid rgba(163, 73, 164, 0.3);
}

[data-theme="dark"] .ui.label.violet {
    background-color: rgba(139, 69, 195, 0.2) !important;
    color: #a855f7;
    border: 1px solid rgba(139, 69, 195, 0.3);
}

[data-theme="dark"] .ui.label.pink {
    background-color: rgba(224, 102, 102, 0.2) !important;
    color: #f472b6;
    border: 1px solid rgba(224, 102, 102, 0.3);
}

[data-theme="dark"] .ui.label.red {
    background-color: rgba(219, 40, 40, 0.2) !important;
    color: #ef4444;
    border: 1px solid rgba(219, 40, 40, 0.3);
}

[data-theme="dark"] .ui.label.black {
    background-color: rgba(128, 128, 128, 0.2) !important;
    color: #9ca3af;
    border: 1px solid rgba(128, 128, 128, 0.3);
}

/* Ensure basic labels also follow dark theme */
[data-theme="dark"] .ui.basic.label {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .ui.basic.label.green {
    color: #4ade80;
    border: 1px solid rgba(33, 186, 69, 0.4);
}

[data-theme="dark"] .ui.basic.label.olive {
    color: #a3e635;
    border: 1px solid rgba(181, 204, 24, 0.4);
}

[data-theme="dark"] .ui.basic.label.orange {
    color: #fb923c;
    border: 1px solid rgba(242, 113, 28, 0.4);
}

[data-theme="dark"] .ui.basic.label.purple {
    color: #c084fc;
    border: 1px solid rgba(163, 73, 164, 0.4);
}

[data-theme="dark"] .ui.basic.label.violet {
    color: #a855f7;
    border: 1px solid rgba(139, 69, 195, 0.4);
}

[data-theme="dark"] .ui.basic.label.pink {
    color: #f472b6;
    border: 1px solid rgba(224, 102, 102, 0.4);
}

[data-theme="dark"] .ui.basic.label.red {
    color: #ef4444;
    border: 1px solid rgba(219, 40, 40, 0.4);
}

[data-theme="dark"] .ui.basic.label.black {
    color: #9ca3af;
    border: 1px solid rgba(128, 128, 128, 0.4);
}

/* ==============================|| LOG PAGE BACKGROUND FIX ||============================== */

/* Fix bright white background in log content area for dark mode */
[data-theme="dark"] .ui.card {
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .ui.card>.content {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.card>.content>.header {
    color: var(--text-primary) !important;
}

/* Fix table background in dark mode */
[data-theme="dark"] .ui.table {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table thead th {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.table tbody tr {
    background-color: var(--card-bg) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.table tbody tr:hover {
    background-color: var(--menu-hover) !important;
}

[data-theme="dark"] .ui.table tbody td {
    border-top: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Fix form backgrounds in dark mode */
[data-theme="dark"] .ui.form {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .ui.form .field>label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .ui.form input:not([type]),
[data-theme="dark"] .ui.form input[type="text"],
[data-theme="dark"] .ui.form input[type="email"],
[data-theme="dark"] .ui.form input[type="search"],
[data-theme="dark"] .ui.form input[type="password"] {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .ui.dropdown {
    background-color: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .ui.form .ui.dropdown>.text {
    color: var(--text-primary) !important;
}

/* ==============================|| MOBILE LOG CARDS STYLING ||============================== */

/* Mobile log card container */
.mobile-log-container {
    padding: 0 !important;
    margin: 0;
    margin-top: 0;
    padding-top: 0;
    background-color: var(--bg-primary) !important;
    width: 100% !important;
    box-sizing: border-box;
    position: relative;
    top: 0;
}

/* Individual mobile log card */
.mobile-log-card {
    background-color: var(--card-bg) !important;
    border-radius: 0px;
    margin: 2px 0px;
    padding: 12px 8px;
    box-shadow: none;
    border: none;
    border-top: none;
    border-bottom: 1px solid var(--border-color) !important;
    border-left: none;
    border-right: none;
    color: var(--text-primary) !important;
}

/* Mobile log card field */
.mobile-log-field {
    margin-bottom: 8px;
    color: var(--text-primary) !important;
}

.mobile-log-field strong {
    color: var(--text-primary) !important;
}

/* Mobile log card detail field with word wrapping */
.mobile-log-detail {
    word-wrap: break-word;
    overflow-wrap: break-word;
    color: var(--text-primary) !important;
}

/* Mobile log header styling */
.mobile-log-header {
    position: static;
    margin: 0px;
    padding: 8px 8px;
    border: none;
    box-shadow: none;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
    background-color: transparent;
    color: var(--text-primary) !important;
}

/* Mobile log refresh button */
.mobile-log-refresh-btn {
    margin-left: 8px;
    padding: 4px;
    min-height: 20px;
    min-width: 20px;
    font-size: 10px;
}

/* Mobile log clickable span */
.mobile-log-clickable {
    cursor: pointer;
    color: var(--text-secondary) !important;
}

/* Dark theme support for mobile log cards */
[data-theme="dark"] .mobile-log-container {
    background-color: var(--bg-primary) !important;
}

[data-theme="dark"] .mobile-log-card {
    background-color: var(--card-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-field {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-field strong {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-detail {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-header {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-log-clickable {
    color: var(--text-secondary) !important;
}

/* ==============================|| UTILITY CLASSES ||============================== */

/* Clickable cursor */
.cursor-pointer {
    cursor: pointer;
}

/* Loading cursor */
.cursor-wait {
    cursor: wait;
}

/* Reduced opacity for loading states */
.opacity-60 {
    opacity: 0.6;
}

/* Margin right 8px */
.margin-right-8 {
    margin-right: 8px;
}

/* User dropdown styling */
.user-dropdown-content {
    display: flex !important;
    flex-direction: column !important;
}

.user-dropdown-name {
    font-weight: bold;
    color: var(--text-primary) !important;
}

.user-dropdown-details {
    font-size: 0.9em;
    color: var(--text-secondary) !important;
}

/* Dark theme support for user dropdown */
[data-theme="dark"] .user-dropdown-name {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .user-dropdown-details {
    color: var(--text-secondary) !important;
}

/* ==============================|| TABLE AND PAGINATION STYLES ||============================== */

/* Table container with proper responsive layout */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 20px;
}

/* Table footer layout - prevents pagination from jumping to top */
.table-footer-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    /* Prevents floating to top */
}

/* Responsive table footer for larger screens */
@media (min-width: 768px) {
    .table-footer-content {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
    }
}

/* Table footer buttons container */
.table-footer-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* Table action buttons - responsive sizing */
.table-action-button {
    min-width: auto;
    padding: 6px 10px;
    font-size: 11px;
    white-space: nowrap;
    flex: 0 0 auto;
}

@media (min-width: 768px) {
    .table-action-button {
        padding: 8px 14px;
        font-size: 13px;
    }
}

/* Table pagination container */
.table-pagination-container {
    display: flex;
    justify-content: center;
}

@media (min-width: 768px) {
    .table-pagination-container {
        justify-content: flex-end;
    }
}

/* Compact pagination styling - like the screenshot */
.table-pagination.ui.pagination,
.ui.table .ui.pagination,
div.table-pagination-container .ui.pagination {
    margin: 0;
    background: transparent;
    border: none;
    box-shadow: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    position: static !important;
    /* Override mobile menu positioning */
}

.table-pagination.ui.pagination .item,
.ui.table .ui.pagination .item,
div.table-pagination-container .ui.pagination .item {
    background-color: var(--bg-secondary, #ffffff);
    color: var(--text-primary, #333333);
    border: 1px solid var(--border-color, #e0e0e0);
    margin: 2px;
    border-radius: 4px;
    min-width: 32px;
    max-width: 60px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.15s ease, border-color 0.15s ease;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    padding: 4px 8px;
    overflow: hidden;
    cursor: pointer;
}

@media (max-width: 767px) {

    .table-pagination.ui.pagination .item,
    .ui.table .ui.pagination .item,
    div.table-pagination-container .ui.pagination .item {
        min-width: 28px;
        height: 28px;
        font-size: 11px;
        padding: 2px 6px;
        margin: 1px;
    }
}

.table-pagination.ui.pagination .item:hover,
.ui.table .ui.pagination .item:hover,
div.table-pagination-container .ui.pagination .item:hover {
    background-color: var(--button-primary, #007bff);
    color: white;
    border-color: var(--button-primary, #007bff);
}

.table-pagination.ui.pagination .item.active,
.ui.table .ui.pagination .item.active,
div.table-pagination-container .ui.pagination .item.active {
    background-color: var(--button-primary, #007bff);
    color: white;
    border-color: var(--button-primary, #007bff);
    font-weight: 600;
}

/* Disable pagination during loading */
.table-pagination.ui.pagination.loading .item,
.ui.table .ui.pagination.loading .item,
div.table-pagination-container .ui.pagination.loading .item {
    pointer-events: none;
    opacity: 0.7;
}

/* Legacy pagination container - exclude our new FixedPagination component */
div.pagination-container:not(.fixed-pagination-container) {
    position: fixed !important;
    bottom: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 1000 !important;
    background-color: var(--card-bg, #ffffff) !important;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 20px var(--shadow-color, rgba(0, 0, 0, 0.15));
    border: 1px solid var(--border-color, #e0e0e0);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Legacy pagination styling - exclude our new component */
div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu {
    margin: 0;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    display: flex !important;
}

div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item {
    background-color: var(--bg-secondary, #ffffff) !important;
    color: var(--text-primary, #333333) !important;
    border: 1px solid var(--border-color, #e0e0e0) !important;
    margin: 0 2px;
    border-radius: 6px;
    min-width: 36px;
    height: 36px;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    visibility: visible;
    opacity: 1;
}

div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item:hover {
    background-color: var(--button-primary, #007bff) !important;
    color: white !important;
    border-color: var(--button-primary, #007bff) !important;
    transform: translateY(-1px);
}

div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item.active {
    background-color: var(--button-primary, #007bff) !important;
    color: white !important;
    border-color: var(--button-primary, #007bff) !important;
    font-weight: 600;
}

/* Dark theme support for legacy pagination - exclude our new component */
[data-theme="dark"] div.pagination-container:not(.fixed-pagination-container) {
    background-color: rgba(42, 42, 42, 0.95) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item:hover,
[data-theme="dark"] div.pagination-container:not(.fixed-pagination-container) .ui.pagination.menu .item.active {
    background-color: var(--button-primary) !important;
    color: white !important;
    border-color: var(--button-primary) !important;
}

/* ==============================|| TABLE STYLES ||============================== */

/* Sortable header cells */
.sortable-header {
    cursor: pointer;
}

.sortable-header:hover {
    background-color: var(--bg-secondary) !important;
}

/* Balance cell with clickable styling */
.clickable-cell {
    cursor: pointer;
}

.clickable-cell:hover {
    background-color: var(--bg-secondary) !important;
}

/* Priority input styling */
.priority-input input {
    max-width: 60px;
}

/* Action buttons container */
.table-actions {
    display: flex;
    align-items: center !important;
    flex-wrap: wrap;
    gap: 2px !important;
    row-gap: 6px !important;
}

/* Dropdown Margin */
.dropdown-margin-left {
    margin-left: 10px;
}

/* Ensure tables have bottom padding */
.ui.table {
    margin-bottom: 20px;
}



/* Icon-only header buttons on medium screens */
@media screen and (min-width: 768px) and (max-width: 1399px) {
    .ui.menu .item {
        padding-left: 0.6em !important;
        padding-right: 0.6em !important;
        min-width: auto;
    }

    /* Ensure icons are properly centered in icon-only mode */
    .ui.menu .item>.icon:only-child {
        margin: 0;
    }

    /* For items with only icons, reduce padding further */
    .ui.menu .item[title] {
        padding-left: 0.5em !important;
        padding-right: 0.5em !important;
    }
}

/* Prevent header overflow and ensure proper layout */
.ui.menu {
    overflow: visible;
}

.ui.menu .ui.container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Ensure right menu stays on the right */
.ui.menu .menu.right {
    margin-left: auto;
    display: flex;
    align-items: center;
}

/* Fix center alignment issue - ensure proper left-right balance */
.ui.menu .ui.container {
    width: 100%;
}

/* Ensure navigation buttons stay left-aligned */
.ui.menu .ui.container>.item:not(.right) {
    margin-right: 0;
}

/* Force proper layout on all screen sizes */
@media screen and (min-width: 1400px) {
    .ui.menu .ui.container {
        justify-content: space-between;
    }
}

/* ==============================|| MOBILE-FIRST RESPONSIVE DESIGN ||============================== */

/* Base mobile styles (0-768px) */
@media screen and (max-width: 768px) {

    /* Ensure proper body spacing for mobile */
    body {
        padding-top: 60px;
        /* Maintain header space */
    }

    /* Ensure navigation menu is properly positioned - exclude pagination */
    .ui.menu:not(.pagination) {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1000 !important;
        height: 60px;
    }

    /* Mobile sidebar menu styling - Fix overlapping text issue and gaps */
    .ui.segment {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 2px 8px var(--shadow-color) !important;
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    .ui.menu.secondary.vertical {
        background-color: var(--card-bg) !important;
        border: none;
        box-shadow: none;
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    .ui.menu.secondary.vertical .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        padding: 12px 16px;
        margin: 0;
        transition: all 0.2s ease;
        border-radius: 0;
        border-left: none;
        border-right: none;
        border-top: none;
    }

    .ui.menu.secondary.vertical .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    .ui.menu.secondary.vertical .item:last-child {
        border-bottom: none;
    }

    /* Fix dropdown items within mobile sidebar to remove gaps */
    .ui.menu.secondary.vertical .item .ui.dropdown {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        margin: 0;
        border-radius: 4px;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 4px 12px var(--shadow-color) !important;
        margin: 0;
        border-radius: 4px;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        padding: 8px 12px;
        margin: 0;
        border-radius: 0;
    }

    .ui.menu.secondary.vertical .item .ui.dropdown .menu .item:hover {
        background-color: var(--menu-hover) !important;
    }

    /* Ensure buttons in mobile sidebar have proper styling */
    .ui.menu.secondary.vertical .item .ui.button {
        background-color: transparent !important;
        color: var(--text-primary) !important;
        border: none;
        padding: 8px 12px;
        font-size: 14px;
    }

    .ui.menu.secondary.vertical .item .ui.button:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Dark theme mobile sidebar styling - ensure no gaps */
    [data-theme="dark"] .ui.segment {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical {
        background-color: var(--card-bg) !important;
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color) !important;
        margin: 0;
        border-radius: 0;
        border-left: none;
        border-right: none;
        border-top: none;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Dark theme dropdown styling in mobile sidebar */
    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu {
        background-color: var(--card-bg) !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu .item {
        background-color: var(--card-bg) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-color);
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.dropdown .menu .item:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.button {
        background-color: transparent !important;
        color: var(--text-primary) !important;
        border: none;
    }

    [data-theme="dark"] .ui.menu.secondary.vertical .item .ui.button:hover {
        background-color: var(--menu-hover) !important;
        color: var(--text-primary) !important;
    }

    /* Reset containers for mobile */
    .ui.container {
        width: 100%;
        margin: 0;
        padding: 8px;
        max-width: none;
    }

    /* Main content with proper padding */
    .main-content {
        padding: 8px;
    }

    /* Dashboard container with proper padding */
    .dashboard-container {
        padding: 8px;
    }

    /* Card styling - clean and minimal */
    .ui.card,
    .ui.cards>.card {
        margin: 8px 0;
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 12px;
    }

    /* Segment styling */
    .ui.segment {
        margin: 8px 0;
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 12px;
    }

    /* Form styling - mobile optimized */
    .ui.form {
        margin: 0;
        border: none;
    }

    .ui.form .field,
    .ui.form .fields {
        margin: 8px 0;
    }

    .ui.form .field>label {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
    }

    /* Form groups - stack vertically on mobile */
    .ui.form .fields {
        flex-direction: column;
    }

    .ui.form .fields>.field {
        width: 100%;
        margin: 4px 0;
    }

    /* Input styling */
    .ui.input,
    .ui.dropdown {
        width: 100%;
        font-size: 16px;
        /* Prevent zoom on iOS */
    }

    /* ==============================|| MOBILE TABLE DESIGN ||============================== */

    /* Hide desktop table structure */
    .ui.table thead {
        display: none;
    }

    /* Table container */
    .ui.table {
        border: none;
        box-shadow: none;
        margin: 8px 0;
        background: transparent;
    }

    /* Transform table rows into cards */
    .ui.table tbody tr {
        display: block;
        background: var(--bg-secondary, #ffffff);
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* Hide deleted rows */
    .ui.table tbody tr[style*="display: none"] {
        display: none;
    }

    /* Table cells as flex items */
    .ui.table tbody tr td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: none;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color, #f0f0f0);
        position: relative;
    }

    .ui.table tbody tr td:last-child {
        border-bottom: none;
        padding-top: 12px;
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    /* Data labels for mobile */
    .ui.table tbody tr td:before {
        content: attr(data-label);
        font-weight: 600;
        color: var(--text-secondary, #666);
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        flex: 0 0 auto;
        margin-right: 12px;
        position: absolute;
        left: 0;
        width: 35%;
    }

    /* Data content */
    .ui.table tbody tr td>*:not(.ui.button) {
        flex: 1;
        text-align: right;
        padding-left: 40%;
    }

    /* Button container in mobile - grid layout */
    .ui.table tbody tr td>div {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;
        width: 100%;
        padding-left: 0;
    }

    /* Mobile button styling - compact and responsive */
    .ui.table tbody .ui.button {
        padding: 6px 8px;
        font-size: 11px;
        font-weight: 500;
        border-radius: 4px;
        min-height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        white-space: nowrap;
        width: 100%;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Edit button spans full width */
    .ui.table tbody .ui.button[color="blue"],
    .ui.table tbody .ui.button.primary {
        grid-column: 1 / -1;
        margin-bottom: 4px;
    }

    /* ==============================|| MOBILE TABLE FOOTER ||============================== */

    /* Table footer - mobile optimized */
    .ui.table tfoot {
        display: block;
        padding: 16px 8px;
        border-top: 1px solid var(--border-color, #e0e0e0);
        background: var(--bg-primary, #ffffff);
    }

    .ui.table tfoot tr {
        display: block;
    }

    .ui.table tfoot td {
        display: block;
        padding: 0;
        border: none;
    }

    /* Footer content layout */
    .table-footer-content {
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }

    /* Footer buttons */
    .table-footer-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .table-footer-buttons .ui.button {
        padding: 8px 16px;
        font-size: 13px;
        border-radius: 6px;
        min-width: 70px;
        flex: 0 1 auto;
    }

    /* Mobile pagination */
    .table-pagination-container {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .ui.table tfoot .ui.pagination {
        display: flex;
        justify-content: center;
        gap: 3px;
        margin: 0;
        background: transparent;
        border: none;
        box-shadow: none;
        flex-wrap: wrap;
    }

    .ui.table tfoot .ui.pagination .item {
        min-width: 32px;
        height: 32px;
        padding: 0;
        margin: 0;
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 6px;
        background: var(--bg-secondary, #ffffff);
        color: var(--text-primary, #333);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .ui.table tfoot .ui.pagination .item:hover {
        background: var(--button-primary, #007bff);
        color: white;
        border-color: var(--button-primary, #007bff);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }

    .ui.table tfoot .ui.pagination .item.active {
        background: var(--button-primary, #007bff);
        color: white;
        border-color: var(--button-primary, #007bff);
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    }

    /* Button groups - stack vertically */
    .ui.buttons {
        flex-direction: column !important;
    }

    .ui.buttons .button {
        margin: 2px 0;
        width: 100%;
    }

    /* Menu - full width */
    .ui.menu:not(.pagination) {
        border: none !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .ui.menu:not(.pagination) .ui.container {
        padding: 0 8px;
    }

    /* Hide elements that take too much space on mobile */
    .hide-on-mobile {
        display: none !important;
    }

    /* Compact statistics */
    .ui.statistic {
        margin: 0.5em 0;
    }

    .ui.statistic>.value {
        font-size: 1.5rem;
    }

    /* Modal adjustments */
    .ui.modal {
        margin: 8px;
        width: calc(100% - 16px);
    }
}

/* ==============================|| DESKTOP TABLE FIX ||============================== */

/* Ensure table structure is correct on desktop */
@media screen and (min-width: 769px) {
    .ui.table {
        display: table !important;
    }

    .ui.table thead {
        display: table-header-group !important;
    }

    .ui.table tbody {
        display: table-row-group !important;
    }

    .ui.table tfoot {
        display: table-footer-group !important;
    }

    .ui.table tbody tr {
        display: table-row !important;
    }

    .ui.table tbody tr td {
        display: table-cell !important;
    }
}

/* ==============================|| TABLET RESPONSIVE DESIGN ||============================== */

/* Tablet styles (769px - 1366px) */
@media screen and (min-width: 769px) and (max-width: 1366px) {
    .ui.container {
        width: auto !important;
        max-width: 100% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0 16px;
    }

    /* Reduce card margins and borders for tablets */
    .ui.card,
    .ui.cards>.card {
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* Table adjustments for tablets */
    .ui.table {
        font-size: 0.9em;
        border-radius: 4px;
    }

    .ui.table th,
    .ui.table td {
        padding: 8px 6px;
    }

    /* Card layout optimization */
    .ui.cards {
        margin-left: -0.5em;
        margin-right: -0.5em;
    }

    .ui.cards>.card {
        margin: 0.5em;
        width: calc(50% - 1em);
    }

    /* Content area adjustments */
    .main-content {
        padding: 12px;
    }

    .dashboard-container {
        padding: 1rem;
        max-width: none;
    }

    /* Form adjustments */
    .ui.form .ui.segment {
        padding: 16px;
    }

    /* Menu adjustments */
    .ui.menu .ui.container {
        padding: 0 16px;
    }
}

/* ==============================|| DESKTOP RESPONSIVE DESIGN ||============================== */

/* Desktop styles (1367px+) */
@media screen and (min-width: 1367px) {
    .ui.container {
        width: 1200px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0;
    }

    /* Restore full styling for desktop */
    .ui.card,
    .ui.cards>.card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ui.table {
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .main-content {
        padding: 16px;
    }

    .dashboard-container {
        padding: 20px 24px 40px;
        max-width: 1600px;
    }
}

/* 优化 Dashboard 网格布局 */
@media screen and (max-width: 1366px) {
    .charts-grid {
        margin: 0 -0.5em;
    }

    .charts-grid .column {
        padding: 0.5em;
    }

    .chart-card {
        margin: 0;
    }

    /* 调整字体大小 */
    .ui.header {
        font-size: 1.1em;
    }

    .stat-value {
        font-size: 0.9em;
    }
}

/* Table pagination styling - compact and visually appealing like screenshot */
.ui.table tfoot .ui.pagination {
    position: static;
    margin-top: 10px;
    margin-bottom: 0;
    background: transparent;
    border: none;
    box-shadow: none;
    display: inline-flex;
    gap: 2px;
}

.ui.table tfoot .ui.pagination .item {
    min-width: 32px;
    max-width: 60px;
    font-size: 13px;
    padding: 6px 8px;
    margin: 0;
    transition: background-color 0.15s ease, border-color 0.15s ease;
    background-color: var(--bg-secondary, #ffffff);
    color: var(--text-primary, #333333);
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 4px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
    line-height: 1;
}

@media (max-width: 767px) {
    .ui.table tfoot .ui.pagination .item {
        min-width: 28px;
        font-size: 11px;
        padding: 4px 6px;
        height: 28px;
    }
}

.ui.table tfoot .ui.pagination .item:hover {
    background-color: var(--button-primary, #007bff);
    color: white;
    border-color: var(--button-primary, #007bff);
}

.ui.table tfoot .ui.pagination .item.active {
    background-color: var(--button-primary, #007bff);
    color: white;
    border-color: var(--button-primary, #007bff);
    font-weight: 600;
}

/* Table footer button styling */
.ui.table tfoot .ui.button {
    min-width: auto;
    padding: 6px 10px;
    font-size: 11px;
    margin-right: 4px;
}

@media (min-width: 768px) {
    .ui.table tfoot .ui.button {
        padding: 8px 12px;
        font-size: 13px;
        margin-right: 6px;
    }
}

/* Override Semantic UI button behavior when needed */
.ui.table .ui.button.ui.button {
    width: auto;
    min-width: auto;
    flex-shrink: 1;
}

/* Table action buttons - responsive and compact */
.ui.table tbody .ui.button {
    min-width: auto;
    max-width: 120px;
    padding: 4px 8px;
    font-size: 10px;
    margin: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    flex: 0 0 auto;
}

@media (min-width: 768px) {
    .ui.table tbody .ui.button {
        padding: 6px 10px;
        font-size: 12px;
        margin: 2px;
        max-width: 150px;
    }
}

/* Action buttons container layout */
.ui.table tbody td>div {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    align-items: center;
}

@media (max-width: 767px) {
    .ui.table .ui.button.ui.button {
        width: auto;
        max-width: 80px;
        min-width: 40px;
    }

    .ui.table tbody td>div {
        flex-direction: column;
        align-items: stretch;
        gap: 1px;
    }

    .ui.table tbody .ui.button {
        width: auto;
        max-width: 80px;
        padding: 3px 6px;
        font-size: 9px;
    }
}
