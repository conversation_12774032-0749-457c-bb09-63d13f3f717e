{"name": "react-template", "version": "0.1.0", "private": true, "dependencies": {"@types/qrcode": "^1.5.5", "axios": "^1.8.3", "history": "^5.3.0", "i18next": "24.2.2", "i18next-browser-languagedetector": "^8.0.4", "marked": "^15.0.7", "moment": "^2.30.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-i18next": "^15.4.1", "react-router-dom": "7.3.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "react-turnstile": "^1.1.4", "recharts": "^2.15.1", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.5", "typescript": "5.8.2"}, "scripts": {"start": "FAST_REFRESH=true react-scripts start", "dev": "FAST_REFRESH=true GENERATE_SOURCEMAP=true react-scripts start", "dev:backend": "FAST_REFRESH=true GENERATE_SOURCEMAP=true REACT_APP_VERSION=$(cat ../../VERSION) react-scripts start", "build": "react-scripts build && rm -rf ../build/default && mv -f build ../build/default", "build:dev": "GENERATE_SOURCEMAP=true react-scripts build && rm -rf ../build/default && mv -f build ../build/default", "build:prod": "DISABLE_ESLINT_PLUGIN='true' REACT_APP_VERSION=$(cat ../../VERSION) react-scripts build && rm -rf ../build/default && mv -f build ../build/default", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"http-proxy-middleware": "^3.0.5", "prettier": "^3.5.3"}, "prettier": {"singleQuote": true, "jsxSingleQuote": true}, "resolutions": {"webpack-dev-server": "^4.15.1"}}