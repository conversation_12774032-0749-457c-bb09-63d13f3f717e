{"name": "react-template", "version": "0.1.0", "private": true, "dependencies": {"@douyinfe/semi-icons": "^2.46.1", "@douyinfe/semi-ui": "^2.46.1", "@types/qrcode": "^1.5.5", "@visactor/react-vchart": "~1.8.8", "@visactor/vchart": "~1.8.8", "@visactor/vchart-semi-theme": "~1.8.8", "axios": "^0.27.2", "history": "^5.3.0", "marked": "^4.1.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-fireworks": "^1.0.4", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-telegram-login": "^1.1.2", "react-toastify": "^9.0.8", "react-turnstile": "^1.0.5", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.3", "usehooks-ts": "^2.9.1"}, "scripts": {"start": "FAST_REFRESH=true react-scripts start", "dev": "FAST_REFRESH=true GENERATE_SOURCEMAP=true react-scripts start", "dev:backend": "FAST_REFRESH=true GENERATE_SOURCEMAP=true REACT_APP_VERSION=$(cat ../../VERSION) PORT=3002 react-scripts start", "build": "react-scripts build && mv -f build ../build/air", "build:dev": "GENERATE_SOURCEMAP=true react-scripts build && rm -rf ../build/air && mv -f build ../build/air", "build:prod": "DISABLE_ESLINT_PLUGIN='true' REACT_APP_VERSION=$(cat ../../VERSION) react-scripts build && rm -rf ../build/air && mv -f build ../build/air", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"prettier": "2.8.8", "typescript": "4.4.2"}, "prettier": {"singleQuote": true, "jsxSingleQuote": true}, "proxy": "http://**************:3000"}