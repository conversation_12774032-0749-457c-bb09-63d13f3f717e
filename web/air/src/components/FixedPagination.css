.fixed-pagination-container {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  /* Ensure visibility and prevent page jumping */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  /* Prevent layout shifts and flickering */
  pointer-events: auto !important;
  will-change: auto !important;
  contain: layout style !important;
}

.fixed-pagination-container .semi-pagination {
  margin: 0;
}

.fixed-pagination-container .semi-pagination-item {
  margin: 0 2px !important;
  border-radius: 6px !important;
  min-width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  background: rgba(24, 144, 255, 0.1) !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
  color: #1890ff !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

.fixed-pagination-container .semi-pagination-item:hover:not(.semi-pagination-item-disabled) {
  background: rgba(24, 144, 255, 0.2) !important;
  border-color: rgba(24, 144, 255, 0.5) !important;
  /* Remove transform to prevent layout shifts */
  transform: none !important;
}

.fixed-pagination-container .semi-pagination-item-active {
  background: #1890ff !important;
  color: white !important;
  border-color: #1890ff !important;
}

.fixed-pagination-container .semi-pagination-item-disabled {
  opacity: 0.4 !important;
  cursor: not-allowed !important;
  background: rgba(0, 0, 0, 0.05) !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
  color: rgba(0, 0, 0, 0.3) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fixed-pagination-container {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
    transform: none !important;
    width: auto !important;
    max-width: calc(100vw - 20px) !important;
    padding: 8px 12px !important;
    /* Ensure visibility on mobile */
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-item {
    min-width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
    margin: 0 1px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-page-size-changer {
    margin-left: 8px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Force show pagination items on mobile - override Semi UI responsive hiding */
@media (max-width: 767px) {
  .fixed-pagination-container .semi-pagination-item {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure ellipsis and page numbers are visible */
  .fixed-pagination-container .semi-pagination-item-disabled {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-page-size-changer {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure the entire pagination component is visible */
  .fixed-pagination-container .semi-pagination {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Prevent page jumping by ensuring pagination doesn't affect document flow */
body {
  scroll-behavior: smooth;
}

/* Ensure pagination buttons don't cause scroll jumps */
.fixed-pagination-container * {
  scroll-behavior: auto !important;
}

.fixed-pagination-container button,
.fixed-pagination-container .semi-button {
  scroll-behavior: auto !important;
}

/* Additional stability fixes to prevent flickering */
.fixed-pagination-container {
  /* Prevent any layout recalculations */
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
  /* Ensure consistent positioning */
  transform: translateX(-50%) translateZ(0) !important;
  /* Prevent hover state from affecting positioning */
  transition: none !important;
}

.fixed-pagination-container:hover {
  /* Ensure hover doesn't change positioning */
  transform: translateX(-50%) translateZ(0) !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .fixed-pagination-container {
    background: rgba(45, 45, 45, 0.95) !important;
    border-color: #444 !important;
    color: white !important;
  }
}
