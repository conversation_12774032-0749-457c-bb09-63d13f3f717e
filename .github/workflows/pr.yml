name: pr-check

on:
  pull_request:
    branches:
      - "master"
      - "main"

jobs:
  go_vet:
    name: Go Vet
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"

      - name: Run go vet
        run: go vet ./...

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: go_vet
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"

      - name: Run tests
        run: go test -race -v ./...
