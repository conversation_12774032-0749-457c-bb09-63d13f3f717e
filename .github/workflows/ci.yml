name: ci

on:
  push:
    branches:
      - "master"
      - "main"
      - "test/ci"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_latest:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push latest
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ppcelery/one-api:latest
          cache-from: type=gha
          # cache-to: type=gha,mode=max

  build_hash:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: <PERSON>gin to <PERSON><PERSON> Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Add SHORT_SHA env property with commit short sha
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: Build and push hash label
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ppcelery/one-api:${{ env.SHORT_SHA }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    runs-on: ubuntu-latest
    needs: build_latest
    steps:
      - name: executing remote ssh commands using password
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.TARGET_HOST }}
          username: ${{ secrets.TARGET_HOST_USERNAME }}
          password: ${{ secrets.TARGET_HOST_PASSWORD }}
          port: ${{ secrets.TARGET_HOST_SSH_PORT }}
          script: |
            docker pull ppcelery/one-api:latest
            cd /home/<USER>/repo/VPS
            docker-compose -f b1-docker-compose.yml up -d --remove-orphans --force-recreate oneapi
            docker ps

  build_arm64_hash:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Add SHORT_SHA env property with commit short sha
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: Build and push arm64 hash label
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ppcelery/one-api:arm64-${{ env.SHORT_SHA }}
          platforms: linux/arm64
          build-args: |
            TARGETARCH=arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max

  build_arm64_latest:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push arm64 latest
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ppcelery/one-api:arm64-latest
          platforms: linux/arm64
          build-args: |
            TARGETARCH=arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max

  build_windows:
    runs-on: windows-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.0"

      - name: Add SHORT_SHA env property with commit short sha
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV
        shell: bash

      - name: Build Windows executables
        run: |
          # Build the standard executable
          go build -trimpath -ldflags "-s -w -X github.com/songquanpeng/one-api/common.Version=$(cat VERSION)" -o one-api.exe

          # Make a copy with the short SHA in the name
          cp one-api.exe "one-api-${{ env.SHORT_SHA }}.exe"
        shell: bash

      - name: Upload Windows executables
        uses: actions/upload-artifact@v4
        with:
          name: one-api-windows
          path: |
            one-api.exe
            one-api-${{ env.SHORT_SHA }}.exe
          retention-days: 90

  create_release:
    needs: [build_windows]
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
      - name: Extract version
        id: extract_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Add SHORT_SHA env property with commit short sha
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV
        shell: bash

      - name: Download Windows artifacts
        uses: actions/download-artifact@v4
        with:
          name: one-api-windows

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          name: Release ${{ steps.extract_version.outputs.VERSION }}
          files: |
            one-api.exe
            one-api-${{ env.SHORT_SHA }}.exe
          draft: false
          prerelease: false
