name: macOS Release
permissions:
  contents: write

on:
  push:
    tags:
      - 'v*.*.*'
      - '!*-alpha*'
      - '!*-preview*'
  workflow_dispatch:
    inputs:
      name:
        description: 'reason'
        required: false
jobs:
  release:
    runs-on: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Check repository URL
        run: |
          REPO_URL=$(git config --get remote.origin.url)
          if [[ $REPO_URL == *"pro" ]]; then
            exit 1
          fi
      - uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Build Frontend
        env:
          CI: ""
        run: |
          cd web
          git describe --tags > VERSION
          REACT_APP_VERSION=$(git describe --tags) chmod u+x ./build.sh && ./build.sh
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: '>=1.18.0'
      - name: Build Backend
        run: |
          go mod download
          go build -ldflags "-X 'github.com/songquanpeng/one-api/common.Version=$(git describe --tags)'" -o one-api-macos
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: one-api-macos
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
